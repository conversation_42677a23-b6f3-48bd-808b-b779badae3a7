import Cocoa
import os.log

/// Utility class for managing screen detection and positioning calculations
class ScreenManager {

    /// Shared instance for singleton access
    static let shared = ScreenManager()

    /// Logger for this class
    private let logger = Logger(subsystem: "com.screenhider.app", category: "ScreenManager")

    /// Cache for screen information to avoid repeated calculations
    private var cachedScreenInfo: ScreenInfo?
    private var lastScreenUpdateTime: Date = Date.distantPast
    private let cacheValidityDuration: TimeInterval = 1.0 // 1 second cache

    private init() {
        logger.info("📺 ScreenManager initialized")
        setupScreenChangeMonitoring()
    }
    
    // MARK: - Screen Detection

    /// Gets the main screen with error handling and logging
    var mainScreen: NSScreen? {
        guard let screen = NSScreen.main else {
            logger.error("❌ No main screen available")
            return nil
        }

        logger.debug("📱 Main screen: \(screen.displayDescription)")
        return screen
    }

    /// Gets all available screens
    var allScreens: [NSScreen] {
        let screens = NSScreen.screens
        logger.debug("📺 Found \(screens.count) screen(s)")
        return screens
    }

    /// Gets the primary screen (the one with the menu bar)
    var primaryScreen: NSScreen? {
        let screen = NSScreen.screens.first { screen in
            screen.frame.origin == .zero
        }

        if let screen = screen {
            logger.debug("🖥️ Primary screen: \(screen.displayDescription)")
        } else {
            logger.warning("⚠️ No primary screen found")
        }

        return screen
    }

    /// Gets the screen containing the specified point
    func screen(containing point: NSPoint) -> NSScreen? {
        return NSScreen.screens.first { screen in
            screen.frame.contains(point)
        }
    }

    /// Gets the largest screen by area
    var largestScreen: NSScreen? {
        return NSScreen.screens.max { screen1, screen2 in
            let area1 = screen1.frame.width * screen1.frame.height
            let area2 = screen2.frame.width * screen2.frame.height
            return area1 < area2
        }
    }
    
    // MARK: - Frame Calculations

    /// Calculates the frame for the right quarter overlay on the main screen
    func rightQuarterFrame() -> NSRect? {
        guard let screen = mainScreen else {
            logger.error("❌ No main screen available for right quarter calculation")
            return nil
        }

        let frame = rightQuarterFrame(for: screen)
        logger.debug("📐 Right quarter frame calculated: \(frame)")
        return frame
    }

    /// Calculates the right quarter frame for a specific screen
    func rightQuarterFrame(for screen: NSScreen) -> NSRect {
        let screenFrame = screen.frame
        let quarterWidth = screenFrame.width / 4

        let frame = NSRect(
            x: screenFrame.origin.x + screenFrame.width - quarterWidth,
            y: screenFrame.origin.y,
            width: quarterWidth,
            height: screenFrame.height
        )

        logger.debug("📐 Calculated right quarter for screen \(screen.displayDescription): \(frame)")
        return frame
    }

    /// Calculates frame for any portion of the screen
    func screenFrame(for portion: ScreenPortion, on screen: NSScreen? = nil) -> NSRect? {
        let targetScreen = screen ?? mainScreen
        guard let targetScreen = targetScreen else {
            logger.error("❌ No screen available for frame calculation")
            return nil
        }

        let frame = screenPortion(portion, for: targetScreen)
        logger.debug("📐 Calculated \(portion) frame: \(frame)")
        return frame
    }
    
    /// Calculates frame for different portions of the screen
    func screenPortion(_ portion: ScreenPortion, for screen: NSScreen) -> NSRect {
        let screenFrame = screen.frame
        
        switch portion {
        case .rightQuarter:
            return rightQuarterFrame(for: screen)
        case .rightHalf:
            let halfWidth = screenFrame.width / 2
            return NSRect(
                x: screenFrame.origin.x + halfWidth,
                y: screenFrame.origin.y,
                width: halfWidth,
                height: screenFrame.height
            )
        case .rightThird:
            let thirdWidth = screenFrame.width / 3
            return NSRect(
                x: screenFrame.origin.x + screenFrame.width - thirdWidth,
                y: screenFrame.origin.y,
                width: thirdWidth,
                height: screenFrame.height
            )
        case .custom(let fraction):
            let customWidth = screenFrame.width * fraction
            return NSRect(
                x: screenFrame.origin.x + screenFrame.width - customWidth,
                y: screenFrame.origin.y,
                width: customWidth,
                height: screenFrame.height
            )
        }
    }
    
    // MARK: - Screen Change Monitoring

    /// Sets up internal screen change monitoring
    private func setupScreenChangeMonitoring() {
        NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleScreenConfigurationChange()
        }
    }

    /// Handles screen configuration changes
    private func handleScreenConfigurationChange() {
        logger.info("📺 Screen configuration changed")
        invalidateCache()
        logScreenConfiguration()
    }

    /// Invalidates the cached screen information
    private func invalidateCache() {
        cachedScreenInfo = nil
        lastScreenUpdateTime = Date.distantPast
        logger.debug("🗑️ Screen info cache invalidated")
    }

    /// Sets up monitoring for screen configuration changes (public API)
    func startMonitoringScreenChanges(callback: @escaping () -> Void) -> NSObjectProtocol {
        logger.debug("👀 Starting screen change monitoring")
        return NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { _ in
            callback()
        }
    }

    /// Stops monitoring screen changes
    func stopMonitoringScreenChanges(_ observer: NSObjectProtocol) {
        logger.debug("🛑 Stopping screen change monitoring")
        NotificationCenter.default.removeObserver(observer)
    }
    
    // MARK: - Screen Information

    /// Gets detailed information about the current screen configuration with caching
    func getScreenInfo() -> ScreenInfo {
        let now = Date()

        // Return cached info if still valid
        if let cached = cachedScreenInfo,
           now.timeIntervalSince(lastScreenUpdateTime) < cacheValidityDuration {
            return cached
        }

        // Generate new screen info
        let screens = NSScreen.screens
        let mainScreen = NSScreen.main

        let info = ScreenInfo(
            totalScreens: screens.count,
            mainScreenFrame: mainScreen?.frame ?? .zero,
            allScreenFrames: screens.map { $0.frame },
            allScreenDescriptions: screens.map { $0.displayDescription },
            hasMultipleScreens: screens.count > 1,
            scaleFactor: mainScreen?.backingScaleFactor ?? 1.0,
            totalDisplayArea: screens.reduce(0) { $0 + ($1.frame.width * $1.frame.height) },
            primaryScreenIndex: screens.firstIndex { $0.frame.origin == .zero } ?? 0
        )

        // Cache the result
        cachedScreenInfo = info
        lastScreenUpdateTime = now

        logger.debug("📊 Screen info updated: \(info.totalScreens) screens, main: \(info.mainScreenFrame)")
        return info
    }

    /// Gets screen information for a specific screen
    func getScreenInfo(for screen: NSScreen) -> DetailedScreenInfo {
        return DetailedScreenInfo(
            frame: screen.frame,
            visibleFrame: screen.visibleFrame,
            backingScaleFactor: screen.backingScaleFactor,
            description: screen.displayDescription,
            isPrimary: screen.isPrimary,
            colorSpace: screen.colorSpace?.localizedName ?? "Unknown"
        )
    }
    
    /// Logs current screen configuration for debugging
    func logScreenConfiguration() {
        let info = getScreenInfo()
        print("=== Screen Configuration ===")
        print("Total screens: \(info.totalScreens)")
        print("Main screen frame: \(info.mainScreenFrame)")
        print("Scale factor: \(info.scaleFactor)")
        print("Multiple screens: \(info.hasMultipleScreens)")
        
        for (index, frame) in info.allScreenFrames.enumerated() {
            print("Screen \(index): \(frame)")
        }
        print("============================")
    }
}

// MARK: - Supporting Types

/// Enum representing different screen portions that can be masked
enum ScreenPortion: CustomStringConvertible {
    case rightQuarter
    case rightHalf
    case rightThird
    case leftQuarter
    case leftHalf
    case topHalf
    case bottomHalf
    case custom(fraction: CGFloat) // fraction of screen width from the right

    var description: String {
        switch self {
        case .rightQuarter: return "Right Quarter"
        case .rightHalf: return "Right Half"
        case .rightThird: return "Right Third"
        case .leftQuarter: return "Left Quarter"
        case .leftHalf: return "Left Half"
        case .topHalf: return "Top Half"
        case .bottomHalf: return "Bottom Half"
        case .custom(let fraction): return "Custom (\(Int(fraction * 100))%)"
        }
    }
}

/// Structure containing comprehensive screen information
struct ScreenInfo {
    let totalScreens: Int
    let mainScreenFrame: NSRect
    let allScreenFrames: [NSRect]
    let allScreenDescriptions: [String]
    let hasMultipleScreens: Bool
    let scaleFactor: CGFloat
    let totalDisplayArea: CGFloat
    let primaryScreenIndex: Int

    /// Human-readable summary
    var summary: String {
        return "Screens: \(totalScreens), Main: \(Int(mainScreenFrame.width))x\(Int(mainScreenFrame.height)), Scale: \(scaleFactor)x"
    }
}

/// Detailed information for a specific screen
struct DetailedScreenInfo {
    let frame: NSRect
    let visibleFrame: NSRect
    let backingScaleFactor: CGFloat
    let description: String
    let isPrimary: Bool
    let colorSpace: String

    /// Screen area in pixels
    var area: CGFloat {
        return frame.width * frame.height
    }

    /// Screen resolution as string
    var resolution: String {
        return "\(Int(frame.width))x\(Int(frame.height))"
    }
}

// MARK: - Extensions

extension NSScreen {
    /// Convenience property to check if this is the primary screen
    var isPrimary: Bool {
        return self.frame.origin == .zero
    }

    /// Gets a human-readable description of the screen
    var displayDescription: String {
        let frame = self.frame
        let scale = self.backingScaleFactor
        let primary = isPrimary ? " (Primary)" : ""
        return "Screen: \(Int(frame.width))x\(Int(frame.height)) @\(scale)x at (\(Int(frame.origin.x)), \(Int(frame.origin.y)))\(primary)"
    }

    /// Gets the screen's resolution as a string
    var resolutionString: String {
        return "\(Int(frame.width))x\(Int(frame.height))"
    }

    /// Gets the screen's area in square pixels
    var area: CGFloat {
        return frame.width * frame.height
    }

    /// Checks if this screen contains the specified point
    func contains(point: NSPoint) -> Bool {
        return frame.contains(point)
    }

    /// Gets the center point of the screen
    var centerPoint: NSPoint {
        return NSPoint(
            x: frame.origin.x + frame.width / 2,
            y: frame.origin.y + frame.height / 2
        )
    }

    /// Checks if this is a Retina display
    var isRetina: Bool {
        return backingScaleFactor > 1.0
    }
}
