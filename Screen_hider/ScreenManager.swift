import Cocoa

/// Utility class for managing screen detection and positioning calculations
class ScreenManager {
    
    /// Shared instance for singleton access
    static let shared = ScreenManager()
    
    private init() {}
    
    // MARK: - Screen Detection
    
    /// Gets the main screen with error handling
    var mainScreen: NSScreen? {
        return NSScreen.main
    }
    
    /// Gets all available screens
    var allScreens: [NSScreen] {
        return NSScreen.screens
    }
    
    /// Gets the primary screen (the one with the menu bar)
    var primaryScreen: NSScreen? {
        return NSScreen.screens.first { screen in
            screen.frame.origin == .zero
        }
    }
    
    // MARK: - Frame Calculations
    
    /// Calculates the frame for the right quarter overlay on the main screen
    func rightQuarterFrame() -> NSRect? {
        guard let screen = mainScreen else {
            print("Error: No main screen available")
            return nil
        }
        
        return rightQuarterFrame(for: screen)
    }
    
    /// Calculates the right quarter frame for a specific screen
    func rightQuarterFrame(for screen: NSScreen) -> NSRect {
        let screenFrame = screen.frame
        let quarterWidth = screenFrame.width / 4
        
        return NSRect(
            x: screenFrame.origin.x + screenFrame.width - quarterWidth,
            y: screenFrame.origin.y,
            width: quarterWidth,
            height: screenFrame.height
        )
    }
    
    /// Calculates frame for different portions of the screen
    func screenPortion(_ portion: ScreenPortion, for screen: NSScreen) -> NSRect {
        let screenFrame = screen.frame
        
        switch portion {
        case .rightQuarter:
            return rightQuarterFrame(for: screen)
        case .rightHalf:
            let halfWidth = screenFrame.width / 2
            return NSRect(
                x: screenFrame.origin.x + halfWidth,
                y: screenFrame.origin.y,
                width: halfWidth,
                height: screenFrame.height
            )
        case .rightThird:
            let thirdWidth = screenFrame.width / 3
            return NSRect(
                x: screenFrame.origin.x + screenFrame.width - thirdWidth,
                y: screenFrame.origin.y,
                width: thirdWidth,
                height: screenFrame.height
            )
        case .custom(let fraction):
            let customWidth = screenFrame.width * fraction
            return NSRect(
                x: screenFrame.origin.x + screenFrame.width - customWidth,
                y: screenFrame.origin.y,
                width: customWidth,
                height: screenFrame.height
            )
        }
    }
    
    // MARK: - Screen Change Monitoring
    
    /// Sets up monitoring for screen configuration changes
    func startMonitoringScreenChanges(callback: @escaping () -> Void) -> NSObjectProtocol {
        return NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { _ in
            callback()
        }
    }
    
    /// Stops monitoring screen changes
    func stopMonitoringScreenChanges(_ observer: NSObjectProtocol) {
        NotificationCenter.default.removeObserver(observer)
    }
    
    // MARK: - Screen Information
    
    /// Gets detailed information about the current screen configuration
    func getScreenInfo() -> ScreenInfo {
        let screens = NSScreen.screens
        let mainScreen = NSScreen.main
        
        return ScreenInfo(
            totalScreens: screens.count,
            mainScreenFrame: mainScreen?.frame ?? .zero,
            allScreenFrames: screens.map { $0.frame },
            hasMultipleScreens: screens.count > 1,
            scaleFactor: mainScreen?.backingScaleFactor ?? 1.0
        )
    }
    
    /// Logs current screen configuration for debugging
    func logScreenConfiguration() {
        let info = getScreenInfo()
        print("=== Screen Configuration ===")
        print("Total screens: \(info.totalScreens)")
        print("Main screen frame: \(info.mainScreenFrame)")
        print("Scale factor: \(info.scaleFactor)")
        print("Multiple screens: \(info.hasMultipleScreens)")
        
        for (index, frame) in info.allScreenFrames.enumerated() {
            print("Screen \(index): \(frame)")
        }
        print("============================")
    }
}

// MARK: - Supporting Types

/// Enum representing different screen portions that can be masked
enum ScreenPortion {
    case rightQuarter
    case rightHalf
    case rightThird
    case custom(fraction: CGFloat) // fraction of screen width from the right
}

/// Structure containing detailed screen information
struct ScreenInfo {
    let totalScreens: Int
    let mainScreenFrame: NSRect
    let allScreenFrames: [NSRect]
    let hasMultipleScreens: Bool
    let scaleFactor: CGFloat
}

// MARK: - Extensions

extension NSScreen {
    /// Convenience property to check if this is the primary screen
    var isPrimary: Bool {
        return self.frame.origin == .zero
    }
    
    /// Gets a human-readable description of the screen
    var displayDescription: String {
        let frame = self.frame
        let scale = self.backingScaleFactor
        return "Screen: \(Int(frame.width))x\(Int(frame.height)) @\(scale)x at (\(Int(frame.origin.x)), \(Int(frame.origin.y)))"
    }
}
