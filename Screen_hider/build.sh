#!/bin/bash

# ScreenHider Build Script
# This script compiles the Swift source files and creates a macOS app bundle

set -e  # Exit on any error

echo "🔨 Building ScreenHider..."

# Check if we're in the right directory
if [ ! -f "main.swift" ]; then
    echo "❌ Error: main.swift not found. Please run this script from the Screen_hider directory."
    exit 1
fi

# Check if Swift compiler is available
if ! command -v swiftc &> /dev/null; then
    echo "❌ Error: Swift compiler not found. Please install Xcode or Swift toolchain."
    exit 1
fi

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -f ScreenHider
rm -rf ScreenHider.app

# Compile Swift files
echo "⚙️  Compiling Swift files..."
swiftc -o ScreenHider \
    main.swift \
    AppDelegate.swift \
    OverlayWindow.swift \
    ScreenManager.swift \
    -framework Cocoa

if [ $? -ne 0 ]; then
    echo "❌ Compilation failed!"
    exit 1
fi

echo "✅ Compilation successful!"

# Create app bundle structure
echo "📦 Creating app bundle..."
mkdir -p ScreenHider.app/Contents/MacOS
mkdir -p ScreenHider.app/Contents/Resources

# Copy executable
cp ScreenHider ScreenHider.app/Contents/MacOS/ScreenHider

# Copy Info.plist
cp Info.plist ScreenHider.app/Contents/Info.plist

# Set executable permissions
chmod +x ScreenHider.app/Contents/MacOS/ScreenHider

# Clean up temporary executable
rm ScreenHider

echo "✅ App bundle created: ScreenHider.app"
echo ""
echo "🚀 To run the application:"
echo "   Double-click ScreenHider.app"
echo "   Or run: open ScreenHider.app"
echo ""
echo "📋 The app will appear in your menu bar with a small rectangle icon."
echo "   Click the icon to toggle the screen overlay on/off."
echo ""
echo "🔍 To test screen sharing:"
echo "   1. Enable the overlay from the menu bar"
echo "   2. Start screen sharing in Zoom/Teams/Meet"
echo "   3. The right quarter should be hidden from viewers"
echo "   4. You can still interact with that area normally"
