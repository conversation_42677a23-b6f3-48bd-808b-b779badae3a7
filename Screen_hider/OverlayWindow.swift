import Cocoa

/// A specialized NSWindow that creates a screen overlay for masking content
/// while remaining transparent to input and excluded from screen capture
class OverlayWindow: NSWindow {
    
    /// The color of the overlay (default: black)
    var overlayColor: NSColor = .black {
        didSet {
            backgroundColor = overlayColor
        }
    }
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: contentRect, styleMask: style, backing: backingStoreType, defer: flag)
        setupOverlayWindow()
    }
    
    /// Configures the window with all necessary properties for screen masking
    private func setupOverlayWindow() {
        // Basic window configuration
        self.styleMask = .borderless
        self.backgroundColor = overlayColor
        self.isOpaque = false
        self.hasShadow = false
        self.ignoresMouseEvents = true
        
        // Window level - use screenSaver level to stay above most content
        // but below system UI elements like notifications
        self.level = .screenSaver
        
        // Collection behavior for proper display across spaces and full screen
        self.collectionBehavior = [
            .canJoinAllSpaces,           // Appears on all Spaces
            .fullScreenAuxiliary,        // Appears over full screen apps
            .stationary,                 // Doesn't participate in Exposé
            .ignoresCycle               // Not included in window cycling
        ]
        
        // Critical: Exclude from screen capture and sharing
        if #available(macOS 11.0, *) {
            self.sharingType = .none
        }
        
        // Note: canBecomeKeyWindow and canBecomeMainWindow are overridden below
        
        // Make sure the window doesn't appear in mission control
        self.isExcludedFromWindowsMenu = true
        
        // Set up the content view
        setupContentView()
    }
    
    /// Sets up the content view with the overlay color
    private func setupContentView() {
        let contentView = NSView(frame: self.contentRect(forFrameRect: self.frame))
        contentView.wantsLayer = true
        contentView.layer?.backgroundColor = overlayColor.cgColor
        self.contentView = contentView
    }
    
    /// Updates the overlay color and refreshes the display
    func updateOverlayColor(_ color: NSColor) {
        overlayColor = color
        contentView?.layer?.backgroundColor = color.cgColor
        display()
    }
    
    /// Positions the window to cover the right 1/4 of the main screen
    func positionForRightQuarter() {
        guard let overlayFrame = ScreenManager.shared.rightQuarterFrame() else {
            print("Warning: Could not calculate right quarter frame")
            return
        }

        setFrame(overlayFrame, display: true)
        print("Positioned overlay at: \(overlayFrame)")
    }
    
    /// Override to prevent the window from being moved
    override func setFrameOrigin(_ point: NSPoint) {
        // Prevent manual repositioning
        super.setFrameOrigin(frame.origin)
    }
    
    /// Override to prevent the window from being resized
    override func setFrame(_ frameRect: NSRect, display flag: Bool) {
        // Only allow programmatic frame changes for positioning
        super.setFrame(frameRect, display: flag)
    }

    /// Removes observers when the window is deallocated
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    /// Override to prevent the window from becoming key
    override var canBecomeKey: Bool {
        return false
    }

    /// Override to prevent the window from becoming main
    override var canBecomeMain: Bool {
        return false
    }
}

/// Extension to handle screen change notifications
extension OverlayWindow {
    
    /// Sets up observers for screen configuration changes
    func setupScreenChangeObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(screenConfigurationChanged),
            name: NSApplication.didChangeScreenParametersNotification,
            object: nil
        )
    }
    
    /// Handles screen configuration changes by repositioning the overlay
    @objc private func screenConfigurationChanged() {
        DispatchQueue.main.async { [weak self] in
            self?.positionForRightQuarter()
        }
    }
}
