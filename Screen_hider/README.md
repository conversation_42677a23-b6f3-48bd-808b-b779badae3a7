# ScreenHider

A macOS desktop application that creates a screen overlay to hide the right quarter of your screen from screen sharing applications (Zoom, Teams, Google Meet, etc.) while maintaining full local interaction with the masked area.

## Features

- **Screen Capture Exclusion**: The overlay is completely invisible to screen sharing and recording applications
- **Input Transparency**: Mouse clicks and keyboard input pass through the overlay to underlying applications
- **Multi-Space Support**: Works across all macOS Spaces and full-screen applications
- **Menu Bar Interface**: Simple menu bar app with toggle controls
- **Configurable Colors**: Choose from black, dark gray, or dark red overlay colors
- **Dynamic Positioning**: Automatically adjusts when screen configuration changes
- **No Dock Icon**: Runs silently in the background as a menu bar application

## System Requirements

- macOS Big Sur (11.0) or later
- Xcode 12.0 or later (for building from source)

## How It Works

### Screen Capture Exclusion

The application uses macOS's `NSWindow.sharingType = .none` API (available from macOS 11.0) to exclude the overlay window from screen capture and sharing. This means:

1. **Local Visibility**: You can see the black overlay on your screen
2. **Screen Share Invisibility**: The overlay does not appear in Zoom, Teams, or any screen recording
3. **Input Transparency**: You can still click and type in the masked area normally

### Technical Implementation

The overlay window is configured with specific properties:

```swift
// Exclude from screen capture (macOS 11.0+)
window.sharingType = .none

// Make transparent to input events
window.ignoresMouseEvents = true

// Stay above other windows but below system UI
window.level = .screenSaver

// Appear on all Spaces and full-screen apps
window.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
```

## Building and Installation

### Option 1: Build with Xcode

1. **Open Terminal** and navigate to the project directory:
   ```bash
   cd Screen_hider
   ```

2. **Create Xcode Project** (if you have Xcode):
   ```bash
   # Create a new Xcode project directory structure
   mkdir -p ScreenHider.xcodeproj
   ```

3. **Build using Swift compiler directly**:
   ```bash
   # Compile all Swift files
   swiftc -o ScreenHider main.swift AppDelegate.swift OverlayWindow.swift ScreenManager.swift -framework Cocoa
   ```

4. **Create Application Bundle**:
   ```bash
   # Create app bundle structure
   mkdir -p ScreenHider.app/Contents/MacOS
   mkdir -p ScreenHider.app/Contents/Resources
   
   # Copy executable
   cp ScreenHider ScreenHider.app/Contents/MacOS/
   
   # Copy Info.plist
   cp Info.plist ScreenHider.app/Contents/
   
   # Make executable
   chmod +x ScreenHider.app/Contents/MacOS/ScreenHider
   ```

### Option 2: Quick Build Script

Create a build script to automate the process:

```bash
#!/bin/bash
# build.sh

echo "Building ScreenHider..."

# Compile Swift files
swiftc -o ScreenHider main.swift AppDelegate.swift OverlayWindow.swift ScreenManager.swift -framework Cocoa

if [ $? -eq 0 ]; then
    echo "Compilation successful!"
    
    # Create app bundle
    rm -rf ScreenHider.app
    mkdir -p ScreenHider.app/Contents/MacOS
    mkdir -p ScreenHider.app/Contents/Resources
    
    # Copy files
    cp ScreenHider ScreenHider.app/Contents/MacOS/
    cp Info.plist ScreenHider.app/Contents/
    
    # Set permissions
    chmod +x ScreenHider.app/Contents/MacOS/ScreenHider
    
    echo "App bundle created: ScreenHider.app"
    echo "You can now run the app by double-clicking ScreenHider.app"
else
    echo "Compilation failed!"
    exit 1
fi
```

## Usage

1. **Launch the Application**:
   - Double-click `ScreenHider.app` or run the executable
   - The app will appear in your menu bar (look for a small rectangle icon)

2. **Toggle the Overlay**:
   - Click the menu bar icon
   - Select "Show Overlay" to enable the screen mask
   - Select "Hide Overlay" to disable it

3. **Change Overlay Color**:
   - Click the menu bar icon
   - Navigate to "Overlay Color" submenu
   - Choose from Black, Dark Gray, or Dark Red

4. **Test Screen Sharing**:
   - Enable the overlay
   - Start a screen share in Zoom, Teams, or Google Meet
   - The right quarter of your screen should be hidden from viewers
   - You can still interact with applications in that area normally

## Troubleshooting

### The overlay appears in screen sharing

- Ensure you're running macOS 11.0 or later
- Restart the application
- Check that the app was compiled with the correct macOS SDK

### The overlay doesn't appear

- Check that the main screen is detected correctly
- Try toggling the overlay off and on again
- Restart the application

### Input events are blocked

- This shouldn't happen with the current configuration
- If it does, quit and restart the application

### Multiple monitor setup

- The overlay will appear on the main screen (the one with the menu bar)
- Screen configuration changes are automatically detected and handled

## Development Notes

### Key Classes

- **`AppDelegate`**: Main application controller and menu bar interface
- **`OverlayWindow`**: Specialized NSWindow with screen capture exclusion
- **`ScreenManager`**: Utility for screen detection and positioning calculations

### Important Configuration

The `Info.plist` includes:
- `LSUIElement`: Hides the app from the Dock
- `LSMinimumSystemVersion`: Requires macOS 11.0+
- `NSHighResolutionCapable`: Supports Retina displays

### Permissions

The app doesn't require special permissions, but macOS may show security dialogs for:
- Running unsigned applications (bypass with right-click → Open)
- Accessibility features (not required for this app)

## License

This project is provided as-is for educational and personal use.

## Support

For issues or questions, please check the troubleshooting section above or review the source code comments for implementation details.
