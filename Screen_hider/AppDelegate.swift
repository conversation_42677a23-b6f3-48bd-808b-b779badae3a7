import Cocoa

/// Main application delegate that manages the overlay window and menu bar interface
class AppDelegate: NSObject, NSApplicationDelegate {
    
    // MARK: - Properties
    
    /// The overlay window instance
    private var overlayWindow: OverlayWindow?
    
    /// Status bar item for menu bar interface
    private var statusItem: NSStatusItem?
    
    /// Menu for the status bar item
    private var menu: NSMenu?
    
    /// Current state of the overlay
    private var isOverlayVisible = false
    
    // MARK: - Application Lifecycle
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        setupStatusBarItem()
        setupOverlayWindow()
        
        // Start with overlay hidden
        hideOverlay()
        
        print("ScreenHider started successfully")
    }
    
    func applicationWillTerminate(_ notification: Notification) {
        hideOverlay()
        statusItem = nil
    }
    
    // MARK: - Status Bar Setup
    
    /// Sets up the status bar item and menu
    private func setupStatusBarItem() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.squareLength)
        
        guard let statusItem = statusItem else {
            print("Error: Could not create status item")
            return
        }
        
        // Set the status bar icon
        if let button = statusItem.button {
            button.image = createStatusBarIcon()
            button.toolTip = "ScreenHider - Click to toggle overlay"
        }
        
        // Create and set up the menu
        setupMenu()
        statusItem.menu = menu
    }
    
    /// Creates a simple icon for the status bar
    private func createStatusBarIcon() -> NSImage {
        let image = NSImage(size: NSSize(width: 18, height: 18))
        image.lockFocus()
        
        // Draw a simple rectangle icon representing the screen mask
        let rect = NSRect(x: 2, y: 2, width: 14, height: 14)
        NSColor.controlTextColor.setFill()
        rect.fill()
        
        // Draw the "masked" area
        let maskedRect = NSRect(x: 10, y: 2, width: 6, height: 14)
        NSColor.systemRed.setFill()
        maskedRect.fill()
        
        image.unlockFocus()
        image.isTemplate = true
        return image
    }
    
    /// Sets up the status bar menu
    private func setupMenu() {
        menu = NSMenu()
        
        // Toggle overlay item
        let toggleItem = NSMenuItem(
            title: "Show Overlay",
            action: #selector(toggleOverlay),
            keyEquivalent: ""
        )
        toggleItem.target = self
        menu?.addItem(toggleItem)
        
        menu?.addItem(NSMenuItem.separator())
        
        // Color options submenu
        let colorSubmenu = NSMenu()
        
        let blackItem = NSMenuItem(title: "Black", action: #selector(setBlackColor), keyEquivalent: "")
        blackItem.target = self
        blackItem.state = .on // Default selection
        colorSubmenu.addItem(blackItem)
        
        let grayItem = NSMenuItem(title: "Dark Gray", action: #selector(setGrayColor), keyEquivalent: "")
        grayItem.target = self
        colorSubmenu.addItem(grayItem)
        
        let redItem = NSMenuItem(title: "Dark Red", action: #selector(setRedColor), keyEquivalent: "")
        redItem.target = self
        colorSubmenu.addItem(redItem)
        
        let colorMenuItem = NSMenuItem(title: "Overlay Color", action: nil, keyEquivalent: "")
        colorMenuItem.submenu = colorSubmenu
        menu?.addItem(colorMenuItem)
        
        menu?.addItem(NSMenuItem.separator())
        
        // About item
        let aboutItem = NSMenuItem(
            title: "About ScreenHider",
            action: #selector(showAbout),
            keyEquivalent: ""
        )
        aboutItem.target = self
        menu?.addItem(aboutItem)
        
        // Quit item
        let quitItem = NSMenuItem(
            title: "Quit ScreenHider",
            action: #selector(quitApplication),
            keyEquivalent: "q"
        )
        quitItem.target = self
        menu?.addItem(quitItem)
    }
    
    // MARK: - Overlay Management
    
    /// Sets up the overlay window
    private func setupOverlayWindow() {
        guard let mainScreen = NSScreen.main else {
            print("Error: Could not get main screen")
            return
        }
        
        overlayWindow = OverlayWindow(
            contentRect: mainScreen.frame,
            styleMask: .borderless,
            backing: .buffered,
            defer: false
        )
        
        overlayWindow?.setupScreenChangeObservers()
        overlayWindow?.positionForRightQuarter()
    }
    
    /// Shows the overlay window
    private func showOverlay() {
        overlayWindow?.orderFront(nil)
        overlayWindow?.positionForRightQuarter()
        isOverlayVisible = true
        updateMenuItemTitles()
    }
    
    /// Hides the overlay window
    private func hideOverlay() {
        overlayWindow?.orderOut(nil)
        isOverlayVisible = false
        updateMenuItemTitles()
    }
    
    /// Updates menu item titles based on current state
    private func updateMenuItemTitles() {
        guard let menu = menu else { return }
        
        if let toggleItem = menu.item(at: 0) {
            toggleItem.title = isOverlayVisible ? "Hide Overlay" : "Show Overlay"
        }
    }
    
    // MARK: - Menu Actions
    
    @objc private func toggleOverlay() {
        if isOverlayVisible {
            hideOverlay()
        } else {
            showOverlay()
        }
    }
    
    @objc private func setBlackColor() {
        overlayWindow?.updateOverlayColor(.black)
        updateColorMenuSelection(selectedTitle: "Black")
    }
    
    @objc private func setGrayColor() {
        overlayWindow?.updateOverlayColor(NSColor.darkGray)
        updateColorMenuSelection(selectedTitle: "Dark Gray")
    }
    
    @objc private func setRedColor() {
        overlayWindow?.updateOverlayColor(NSColor.systemRed.withAlphaComponent(0.8))
        updateColorMenuSelection(selectedTitle: "Dark Red")
    }
    
    /// Updates the color menu selection state
    private func updateColorMenuSelection(selectedTitle: String) {
        guard let menu = menu,
              let colorMenuItem = menu.item(withTitle: "Overlay Color"),
              let colorSubmenu = colorMenuItem.submenu else { return }
        
        for item in colorSubmenu.items {
            item.state = (item.title == selectedTitle) ? .on : .off
        }
    }
    
    @objc private func showAbout() {
        let alert = NSAlert()
        alert.messageText = "ScreenHider"
        alert.informativeText = """
        Version 1.0
        
        A macOS application that creates a screen overlay to hide the right quarter of your screen from screen sharing applications while maintaining full local interaction.
        
        Features:
        • Excludes overlay from screen capture and sharing
        • Transparent to mouse and keyboard input
        • Works across all Spaces and full-screen apps
        • Configurable overlay colors
        
        Compatible with macOS 11.0 and above.
        """
        alert.alertStyle = .informational
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    @objc private func quitApplication() {
        NSApplication.shared.terminate(self)
    }
}
